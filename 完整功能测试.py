#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 完整功能测试
验证所有核心功能的集成测试脚本

作者: CogniGraph™ AI工作流系统
创建时间: 2025-07-25
版本: v2.2.1
"""

import sys
import os
import time
from pathlib import Path
from typing import List, Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from 配置管理器 import ConfigManager
from 浏览器管理器 import BrowserManager
from 快捷方式管理器 import ShortcutManager
from 启动管理器 import StartupManager

class IntegrationTester:
    """集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.config_manager = ConfigManager()
        self.browser_manager = BrowserManager(self.config_manager)
        self.shortcut_manager = ShortcutManager(self.config_manager)
        self.startup_manager = StartupManager()
        
        self.test_browser_name = "集成测试浏览器"
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        
        print(result)
        self.test_results.append({
            "name": test_name,
            "success": success,
            "message": message
        })
        
        return success
    
    def test_environment_check(self) -> bool:
        """测试环境检查"""
        print("\n🔍 环境检查测试")
        print("-" * 40)
        
        # Python版本检查
        python_ok, python_info = self.startup_manager.check_python_version()
        self.log_test("Python版本检查", python_ok, python_info)
        
        # 依赖检查
        deps_results = self.startup_manager.check_dependencies()
        all_deps_ok = all(is_available for _, is_available, _ in deps_results)
        self.log_test("依赖模块检查", all_deps_ok, f"检查了 {len(deps_results)} 个模块")
        
        # 文件检查
        files_results = self.startup_manager.check_required_files()
        all_files_ok = all(exists for _, exists, _ in files_results)
        self.log_test("必需文件检查", all_files_ok, f"检查了 {len(files_results)} 个文件")
        
        return python_ok and all_deps_ok and all_files_ok
    
    def test_config_manager(self) -> bool:
        """测试配置管理器"""
        print("\n⚙️ 配置管理器测试")
        print("-" * 40)
        
        # 配置加载测试
        config_loaded = bool(self.config_manager.config_data)
        self.log_test("配置文件加载", config_loaded)
        
        # 配置读取测试
        project_name = self.config_manager.get("project_info.name")
        read_success = project_name == "浏览器多账号绿色版"
        self.log_test("配置读取", read_success, f"项目名称: {project_name}")
        
        # 配置验证测试
        errors = self.config_manager.validate_config()
        validation_success = len(errors) == 0
        self.log_test("配置验证", validation_success, f"发现 {len(errors)} 个错误")
        
        # 功能开关测试
        plugin_sync_enabled = self.config_manager.is_feature_enabled("enable_plugin_sync")
        self.log_test("功能开关读取", True, f"插件同步: {'启用' if plugin_sync_enabled else '禁用'}")
        
        return config_loaded and read_success and validation_success
    
    def test_browser_management(self) -> bool:
        """测试浏览器管理功能"""
        print("\n🌐 浏览器管理测试")
        print("-" * 40)
        
        # 清理可能存在的测试浏览器
        if self.browser_manager.browser_exists(self.test_browser_name):
            self.browser_manager.delete_browser(self.test_browser_name, delete_shortcut=False)
        
        # 测试浏览器列表
        initial_browsers = self.browser_manager.list_browsers()
        self.log_test("浏览器列表获取", True, f"现有 {len(initial_browsers)} 个浏览器")
        
        # 测试创建浏览器
        create_success = self.browser_manager.create_browser_instance(
            browser_name=self.test_browser_name,
            icon_type="chrome",
            create_shortcut=False
        )
        self.log_test("浏览器创建", create_success, f"创建浏览器: {self.test_browser_name}")
        
        if not create_success:
            return False
        
        # 测试浏览器存在检查
        exists = self.browser_manager.browser_exists(self.test_browser_name)
        self.log_test("浏览器存在检查", exists)
        
        # 测试浏览器列表更新
        updated_browsers = self.browser_manager.list_browsers()
        list_updated = len(updated_browsers) == len(initial_browsers) + 1
        self.log_test("浏览器列表更新", list_updated, f"现有 {len(updated_browsers)} 个浏览器")
        
        # 测试删除浏览器
        delete_success = self.browser_manager.delete_browser(self.test_browser_name, delete_shortcut=False)
        self.log_test("浏览器删除", delete_success)
        
        # 验证删除后状态
        final_browsers = self.browser_manager.list_browsers()
        delete_verified = len(final_browsers) == len(initial_browsers)
        self.log_test("删除验证", delete_verified, f"恢复到 {len(final_browsers)} 个浏览器")
        
        return create_success and exists and list_updated and delete_success and delete_verified
    
    def test_shortcut_management(self) -> bool:
        """测试快捷方式管理功能"""
        print("\n🔗 快捷方式管理测试")
        print("-" * 40)
        
        # 测试列出桌面快捷方式
        shortcuts = self.shortcut_manager.list_desktop_shortcuts()
        self.log_test("桌面快捷方式列表", True, f"找到 {len(shortcuts)} 个快捷方式")
        
        # 测试创建快捷方式
        test_target = str(Path(__file__).parent / "启动管理器.py")
        create_success = self.shortcut_manager.create_desktop_shortcut(
            browser_name="测试快捷方式",
            target_path=test_target,
            working_directory=str(Path(__file__).parent)
        )
        self.log_test("快捷方式创建", create_success)
        
        if create_success:
            # 测试删除快捷方式
            delete_success = self.shortcut_manager.delete_shortcut("测试快捷方式")
            self.log_test("快捷方式删除", delete_success)
            return delete_success
        
        return create_success
    
    def test_project_structure(self) -> bool:
        """测试项目结构"""
        print("\n📁 项目结构测试")
        print("-" * 40)
        
        project_root = Path(__file__).parent
        
        # 检查核心文件
        core_files = [
            "browsers.cognigraph.json",
            "README.md",
            "requirements.txt",
            "项目配置.json",
            "配置管理器.py",
            "启动管理器.py",
            "浏览器管理器.py",
            "浏览器管理器GUI.py",
            "快捷方式管理器.py"
        ]
        
        missing_files = []
        for file_name in core_files:
            file_path = project_root / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        files_complete = len(missing_files) == 0
        self.log_test("核心文件完整性", files_complete, 
                     f"缺失文件: {missing_files}" if missing_files else "所有文件都存在")
        
        # 检查目录结构
        required_dirs = [
            "GoogleChromePortable",
            "默认图标",
            "浏览器实例",
            "备份",
            "日志"
        ]
        
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            if not dir_path.exists():
                missing_dirs.append(dir_name)
        
        dirs_complete = len(missing_dirs) == 0
        self.log_test("目录结构完整性", dirs_complete,
                     f"缺失目录: {missing_dirs}" if missing_dirs else "所有目录都存在")
        
        # 检查Chrome Portable
        chrome_exe = project_root / "GoogleChromePortable" / "GoogleChromePortable.exe"
        chrome_available = chrome_exe.exists()
        self.log_test("Chrome Portable可用性", chrome_available)
        
        return files_complete and dirs_complete and chrome_available
    
    def test_feature_flags(self) -> bool:
        """测试功能开关"""
        print("\n🎛️ 功能开关测试")
        print("-" * 40)
        
        feature_flags = self.config_manager.get_feature_flags()
        
        # 测试重要功能开关
        important_features = [
            "enable_plugin_sync",
            "enable_icon_download", 
            "enable_theme_switching",
            "enable_desktop_shortcuts"
        ]
        
        all_features_ok = True
        for feature in important_features:
            enabled = feature_flags.get(feature, False)
            feature_name = feature.replace("enable_", "").replace("_", " ").title()
            self.log_test(f"{feature_name}功能", True, "启用" if enabled else "禁用")
        
        return all_features_ok
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🧪 浏览器多账号绿色版 v2.2.1 - 完整功能测试")
        print("=" * 60)
        
        # 运行各项测试
        env_ok = self.test_environment_check()
        config_ok = self.test_config_manager()
        browser_ok = self.test_browser_management()
        shortcut_ok = self.test_shortcut_management()
        structure_ok = self.test_project_structure()
        features_ok = self.test_feature_flags()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 测试结果统计")
        print("-" * 40)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 总体评估
        all_critical_ok = env_ok and config_ok and browser_ok and structure_ok
        
        if all_critical_ok:
            print(f"\n🎉 集成测试通过！项目核心功能正常。")
            if shortcut_ok and features_ok:
                print(f"✨ 所有功能测试通过，项目状态优秀！")
            return True
        else:
            print(f"\n⚠️ 集成测试发现问题，请检查失败的测试项。")
            return False


def main():
    """主函数"""
    tester = IntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print(f"\n🚀 项目已准备就绪，可以正常使用！")
        print(f"💡 使用 'python 启动管理器.py' 启动程序")
    else:
        print(f"\n🔧 请修复测试中发现的问题后重新测试")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
